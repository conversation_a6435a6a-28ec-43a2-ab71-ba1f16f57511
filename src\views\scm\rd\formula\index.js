import {
  Formula<PERSON>pi
} from "@/api/scm/rd/formula";
import { MaterialApi } from "@/api/scm/base/material";
import { CompanyApi } from "@/api/scm/base/company";
import { ref, computed, reactive, toRefs, nextTick, getCurrentInstance, watch, onMounted } from "vue";
import ExcelJS from "exceljs"; // 导入ExcelJS
import { saveAs } from "file-saver";
import _ from "lodash"; 
import axios from "axios";
import { useExportHelpers } from "@/views/scm/quote/apply/export-helpers";
import { ElMessage } from "element-plus";
import { id } from "element-plus/es/locale/index.mjs";
import { status } from "nprogress";
import { getDictOptions,DICT_TYPE,getDictLabel } from "@/utils/dict";
import { useMessage } from '@/hooks/web/useMessage';

const message = useMessage();
// 将所有逻辑封装在一个函数中导出
export function useFormulaFunctions(proxy) {
  // 如果未提供proxy参数，尝试从当前组件获取
  if (!proxy) {
    const instance = getCurrentInstance();
    proxy = instance?.proxy;
  }
  
const product_category = (getDictOptions),
    product_state = getDictOptions(DICT_TYPE.PRODUCT_STATE),
    medium_trace_element = getDictOptions(DICT_TYPE.MEDIUM_TRACE_ELEMENT),
    approve_status = getDictOptions(DICT_TYPE.APPROVE_STATUS);

  const refresh = ref(true);
  const formulaList = ref([]);
  const formulaId = ref(null);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");
  const defaultRatio = ref(1000);
  const approveOpen = ref(false);
  const approveTitle = ref("审批");
  const nowPage = ref("formula");
  // 添加导出状态变量
  const exporting = ref(false);
  const isCreateFormula = ref(false);
  const selectedFormula = ref(null);

  // 预览相关变量
  const previewOpen = ref(false);
  const previewData = ref({});
  const saving = ref(false);
  const historyOpen = ref(false);
  const isUpdate = ref(false);
  const activeTab = ref("basic");
  const loadingFormula = ref(false);
  const options = ref([]);
  const operate = ref('addFormula')
  
  const data = reactive({
    form: {},
    queryParams: {
      pageNo: 1,
      pageSize: 10,
      name: undefined,
      companyName: undefined,
      stateCode: undefined,
      typeCode: undefined,
      totalAmount: undefined,
      totalCost: undefined,
    },
    rules: {
      name: [{ required: true, message: "配方名称不能为空", trigger: "blur" }],
      totalCost: [{ required: true, message: "总成本不能为空", trigger: "blur" }],
      createBy: [
        { required: true, message: "创建者ID不能为空", trigger: "blur" },
      ],
      createTime: [
        { required: true, message: "创建时间不能为空", trigger: "blur" },
      ],
      delFlag: [{ required: true, message: "删除标记不能为空", trigger: "blur" }],
    },
    approveRules: {
      approveDesc: [
        { required: true, message: "审批意见不能为空", trigger: "blur" },
      ],
      status: [{ required: true, message: "审批状态不能为空", trigger: "blur" }],
    },
  });

  const { queryParams, form, approveRules } = toRefs(data);

  // 添加一个计算属性以安全地访问elements对象
  const safeElements = computed(() => {
    return form.value.elements || {};
  });

  /** 查询配方信息列表 */
  function getList() {
    loading.value = true;
    FormulaApi.getFormulaPage(queryParams.value).then((response) => {
      formulaList.value = response.list;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
    // 这里需要添加重置表格加载状态的代码
    loading.value = false;
  }

  // 表单重置
  function reset() {
    // 先将isUpdate设置为true, 确保组件卸载顺序正确
    isUpdate.value = true;

    // 然后清空表单数据
    form.value = {
      id: null,
      name: null,
      typeCode: null,
      nameRule: null,
      companyName: null,
      companyCode: null,
      companyId: null,
      stateCode: null,
      npk: {
        N: null,
        P: null,
        PType: "P",
        K: null,
      },
      microElement: [{ element: null, quantity: null, unit: null }],
      otherMaterial: [{ element: null, quantity: null, unit: null }],
      otherMaterialQuantity: null,
      otherMaterialUnit: null,
      elements: {},
      applyId: null,
      totalAmount: null,
      totalCost: 0,
      totalRawCost: 0,
      totalOtherCost: 0,
      totalAuxiliaryCost: 0,
      processDesc: null,
      remark: null,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      materials: [],
      materialsSum: null,
      density: 1,
      ph: null,
    };
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNo = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    // 先执行重置操作，清空所有数据
    reset();
    // 设置为编辑模式
    isUpdate.value = true;
    // 清空formulaId，以确保不会加载任何现有数据
    formulaId.value = null;
    operate.value = 'addFormula'
    // 强制重置form对象，避免任何残留数据
    form.value = {
      id: null,
      name: null,
      typeCode: null,
      stateCode: null,
      companyName: null,
      companyId: null,
      npk: { N: null, P: null, PType: "P", K: null },
      microElement: [{ element: null, quantity: null, unit: null }],
      otherMaterial: [{ element: "", quantity: "0", unit: "元" }],
      materials: [],
      totalAmount: 0,
      totalCost: 0,
      totalRawCost: 0,
      totalOtherCost: 0,
      totalAuxiliaryCost: 0,
      quoteFlag: 0,
    };
    
    // 显示对话框并设置标题
    open.value = true;
    title.value = "添加配方信息";
    isCreateFormula.value = true;
    
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    const _formulaId = row.id || undefined;
    if (!_formulaId) {
      message.error("请选择需要修改的记录");
      return;
    }
    reset();
    formulaId.value = _formulaId;
    
    loading.value = true;
    
    // 传递 refresh=true, arg1=false
    FormulaApi.getFormula(_formulaId,true) 
      .then((response) => {
        // 健壮性检查：response 本身有效（包含 id）
        if (response && response.id) { 
          // 直接将整个 response 对象赋值给 form.value
          Object.keys(form.value).forEach(key => { delete form.value[key]; }); // 清除现有键
          Object.assign(form.value, response); 
          
          open.value = true; // 获取成功后才打开弹窗
          title.value = "修改配方";
          isUpdate.value = true;
          operate.value = 'updateFormula';
        }
      })
      .catch(error => {
        message.error("加载配方数据时出错"); // 更具体的错误信息
      })
      .finally(() => {
        loading.value = false; // 确保总是关闭加载状态
      });
  }

  /** 提交按钮 */
  function submitForm() {
    
    // 获取子组件实例
    const formulaAddRef = proxy.$refs.formulaAddRef;
    if (!formulaAddRef) {
      return;
    }
    
    // 先直接获取表单数据，跳过验证过程
    try {
      let formData = null;
      
      // 尝试从子组件获取表单数据
      if (typeof formulaAddRef.getFormData === 'function') {
        formData = formulaAddRef.getFormData();
      } else if (formulaAddRef.form) {
        formData = formulaAddRef.form;
      } else {
        formData = form.value;
      }
      
      // 检查表单数据是否有效
      if (!formData) {
        message.error("获取表单数据失败");
        return;
      }
      formData.id = formulaId.value
      if (formData.materials && Array.isArray(formData.materials)) {
        formData.materials = formData.materials.filter(item => item && item.materialName);
      }
      if (formData.packages && Array.isArray(formData.packages)) {
        formData.packages = formData.packages.filter(item => item && item.materialName);
      }
      // 提交表单数据
      if (formData.id != null) {
        // 更新操作前确保所有关联数据的formulaId与配方id一致
        if (formData.materials && Array.isArray(formData.materials)) {
          formData.materials = formData.materials.map(material => {
            // 处理element字段，确保是对象格式
            let processedElement = material.element;
            if (typeof material.element === 'string') {
              try {
                processedElement = JSON.parse(material.element);
              } catch (e) {
                processedElement = {};
              }
            } else if (!material.element || typeof material.element !== 'object') {
              processedElement = {};
            }
            
            return {
              ...material,
              element: processedElement,
              formulaId: formData.id // 确保material的formulaId与配方id一致
            };
          });
        }
        
        if (formData.packages && Array.isArray(formData.packages)) {
          formData.packages = formData.packages.map(pkg => {
            // 处理element字段，确保是对象格式
            let processedElement = pkg.element;
            if (typeof pkg.element === 'string') {
              try {
                processedElement = JSON.parse(pkg.element);
              } catch (e) {
                processedElement = {};
              }
            } else if (!pkg.element || typeof pkg.element !== 'object') {
              processedElement = {};
            }
            
            // 构建包材对象，处理materialId和id
            const newPkg = {
              ...pkg,
              element: processedElement,
              formulaId: formData.id // 确保package的formulaId与配方id一致
            };
            
            // 如果存在materialId但没有id，则使用materialId作为id
            if (pkg.materialId && !pkg.id) {
              newPkg.id = pkg.materialId;
            }
            
            return newPkg;
          });
        }
        
        if (formData.auxiliary && Array.isArray(formData.auxiliary)) {
          formData.auxiliary = formData.auxiliary.map(aux => ({
            ...aux,
            formulaId: formData.id // 确保auxiliary的formulaId与配方id一致
          }));
        }

        // 更新操作
        FormulaApi.updateFormula(formData).then(response => {
          message.success("修改成功");
          open.value = false;
          getList();
        }).catch(error => {
          message.error("更新失败 ");
        });
      } else {
        // 新增操作
        FormulaApi.createFormula(formData).then(response => {
          message.success("新增成功");
          open.value = false;
          getList();
        }).catch(error => {
          message.error("新增失败");
        });
      }
    } catch (error) {
      message.error("提交失败");
    }
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    if (!_ids || _ids.length === 0) {
      message.error("请选择要删除的记录");
      return;
    }
    
    const hasApprovedRecords = formulaList.value
      .filter(item => Array.isArray(_ids) ? _ids.includes(item.id) : item.id === _ids)
      .some(item => item.status === 3);
    
    if (hasApprovedRecords) {
      message.error('选择的记录中包含已审核通过的记录');
      return;
    }

    message.confirm('是否确认删除？').then(function () {
      return FormulaApi.deleteFormula(_ids);
    }).then(() => {
      getList();
      message.success("删除成功");
    }).catch(() => {});
  }

  const approveForm = ref({})
  /** 审核操作 */
  function handleApprove(row) {
    const _formulaId = row.id;
    if (!_formulaId) {
      message.error("请选择需要审核的记录");
      return;
    }
    
    loading.value = true; 
    
    FormulaApi.getFormula(_formulaId,true).then((response) => {
      // 健壮性检查：response 本身有效
      if (response && response.id) { 
        approveForm.value = response;
        approveOpen.value = true;
      } else {
        message.error("获取配方数据失败：无法打开审核窗口");
      }
    }).catch(error => {
      message.error("加载配方数据进行审核时出错");
    }).finally(() => {
      loading.value = false;
    });
  }
  const handleSendToApprove =async (row) => {
    const _formulaId = row.id;
    if(!_formulaId){
      message.error('请选择需要提交的记录')
      return;
    }
    const response = await FormulaApi.getFormula(_formulaId,true);
    if(response){
      response.status = 1;
      const result = await FormulaApi.approveFormula(response);
      if(result){
        message.success('提交成功')
        getList();
      }
    }
  }

  /** 提交审核 */
  function submitApprove() {
    const approveRef = proxy.$refs["approveRef"];
    
    if (!approveRef) {
      message.error("审批表单加载失败，请刷新页面重试");
      return;
    }
    
    approveRef.validate((valid) => {
      if (valid) {
        approveForm.value.approveTime = new Date().getTime();
        FormulaApi.approveFormula(approveForm.value).then(response => {
          message.success("审核成功");
          approveOpen.value = false;
          getList();
        }).catch(error => {
          message.error("审核失败");
        });
      } else {
        message.error("请完善审批信息");
      }
    });
  }

  /** 取消审核 */
  function cancelApprove() {
    approveOpen.value = false;
    approveForm.value = {};
  }

  /** 对话框中的审核按钮操作 */
  function handleDialogApprove() {
    const _formulaId = formulaId.value;
    if (!_formulaId) {
      message.error("当前配方ID丢失，无法进行审核");
      return;
    }
    
    loading.value = true; 
    
    FormulaApi.getFormula(_formulaId,true).then((response) => {
      // 健壮性检查：response 本身有效
      if (response && response.id) {
        // 仅更新现有 form.value 中的 status 和 approveDesc
        form.value.status = response.status !== undefined ? response.status : 0;
        form.value.approveDesc = response.approveDesc || "";

        approveOpen.value = true;
        approveTitle.value = "配方信息审核";
      } else {
        message.error("获取最新配方数据失败：无法打开审核窗口");
      }
    }).catch(error => {
      message.error("加载最新配方数据进行审核时出错" );
    }).finally(() => {
      loading.value = false;
    });
  };

  /** 查看历史记录 */
  function openHistory(row) {
    selectedFormula.value = row;
    historyOpen.value = true;
  }

  /** 关闭历史记录对话框 */
  function closeHistory() {
    historyOpen.value = false;
    selectedFormula.value = null;
  }

  /** 对话框关闭处理 */
  function handleDialogClose() {
    // 立即重置加载状态
    loading.value = false;
    
    // 重置isUpdate状态，防止下次打开时沿用上次的状态
    isUpdate.value = true;
    
    // 立即清空formulaId，以确保下次操作时不会加载旧数据
    formulaId.value = null;
    
    // 重置activeTab，确保下次打开时默认显示基本信息
    activeTab.value = "basic";
    
    // 如果子组件存在，使用clear方法进行完全清理
    if (proxy.$refs.formulaAddRef) {
      try {
        // 首选使用clear方法进行彻底清理
        if (typeof proxy.$refs.formulaAddRef.clear === "function") {
          proxy.$refs.formulaAddRef.clear();
        } else if (typeof proxy.$refs.formulaAddRef.reset === "function") {
          proxy.$refs.formulaAddRef.reset();
        }
      } catch (error) {
      }
    }
  }

  /** 提交成功处理 */
  const handleSubmitSuccess = (data) => {
    // 关闭对话框
    open.value = false;

    // 重置状态
    reset();
    formulaId.value = null;

    // 刷新列表数据
    getList();
  };

  /** 提交关闭处理 */
  const handleSubmitClose = () => {
    // 确保对话框已关闭
    if (open.value) {
      open.value = false;

      // 重置状态
      reset();
      formulaId.value = null;

      // 刷新列表数据
      getList();
    }
  };

  // 转换元素含量数据为表格数据格式
  const elementTableData = computed(() => {
    if (!safeElements.value || Object.keys(safeElements.value).length === 0) {
      return [];
    }
    
    // 将元素含量数据转换为表格行
    return [safeElements.value];
  });

  // 定义表格列
  const elementColumns = computed(() => {
    if (!safeElements.value) return [];
    
    return Object.keys(safeElements.value).map(key => {
      return {
        prop: key,
        label: key,
        type: 'progress',
        width: ''
      };
    });
  });

  // 计算属性用于过滤有效的微量元素
  const filteredMicroElements = computed(() => {
    if (!form.value || !form.value.microElement) return [];
    return form.value.microElement.filter(
      (el) => el && el.element && el.quantity
    );
  });

  // 同样为其他元素添加
  const filteredOtherMaterials = computed(() => {
    if (!form.value || !form.value.otherMaterial) return [];
    return form.value.otherMaterial.filter(
      (el) => el && el.element && el.quantity
    );
  });

  // 预览数据的计算属性
  const previewFilteredMicroElements = computed(() => {
    if (!previewData.value || !previewData.value.microElement) return [];
    return previewData.value.microElement.filter(
      (el) => el && el.element && el.quantity
    );
  });

  const previewFilteredOtherMaterials = computed(() => {
    if (!previewData.value || !previewData.value.otherMaterial) return [];
    return previewData.value.otherMaterial.filter(
      (el) => el && el.element && el.quantity
    );
  });

  // 表格行样式
  const tableRowClassName = ({ rowIndex }) => {
    // 确保即使有空数据行也能正常显示样式
    return rowIndex % 2 === 0 ? "even-row" : "odd-row";
  };

  const displayedElements = computed(() => {
    const elements = new Set();
    if (!form.value.materials || form.value.materials.length === 0) {
      return [];
    }
    
    form.value.materials.forEach((material) => {
      // 增加对 material.element 为 null 的判断
      if (material && material.element && typeof material.element === "object") {
        Object.keys(material.element).forEach((ele) => elements.add(ele));
      }
    });
    
    return Array.from(elements);
  });

  const tableData = computed(() => {
    return form.value.materials || [];
  });

  // 设置小数位数，例如保留两位小数
  const decimalPlaces = 4;

  // 函数：去除字符串数字中小数点后不必要的0
  function unpad(num) {
    // 先转换为字符串，确保能够应用正则表达式
    const strNum = num.toString();
    // 使用正则表达式去除小数点后不必要的0
    return strNum.replace(/(\.\d*?)0+$/, "$1").replace(/\.$/, "");
  }

  // 创建防抖更新函数
  const updateFormStats = _.debounce((totalAmount, totalRawCost, elements) => {
    // 使用nextTick确保在下一个DOM更新周期执行，避免递归触发
    nextTick(() => {
      // 仅当值实际变化时才更新，减少不必要的响应式触发
      if (form.value.totalAmount !== totalAmount) {
        form.value.totalAmount = totalAmount;
      }

      if (form.value.totalRawCost !== totalRawCost) {
        form.value.totalRawCost = totalRawCost;
      }

      // 计算总成本
      const newTotalCost =
        Number(totalRawCost || 0) +
        Number(form.value.totalAuxiliaryCost || 0) +
        Number(form.value.totalOtherCost || 0);

      if (form.value.totalCost !== newTotalCost) {
        form.value.totalCost = newTotalCost;
      }

      // 使用一次性替换元素对象，而非逐个属性修改
      if (elements && Object.keys(elements).length > 0) {
        // 检查是否有实际变化
        const needsUpdate =
          !form.value.elements ||
          JSON.stringify(form.value.elements) !== JSON.stringify(elements);

        if (needsUpdate) {
          form.value.elements = { ...elements };
        }
      }
    });
  }, 300); // 300ms防抖延迟

  // 修改summary方法，避免递归触发
  const summaryMethod = (params) => {
    try {
      const { columns, data } = params;
      const sums = [];

      // 临时存储计算结果，避免直接修改响应式数据
      let calculatedTotalAmount = 0;
      let calculatedRawCost = 0;
      let calculatedElements = {};
      let npkTotal = 0;

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "";
          return;
        }
        if (index === 1) {
          // 使用h函数创建虚拟DOM
          sums[index] = "合计";
          return;
        }

        if (column.property === "price") {
          sums[index] = "";
          return;
        }

        // 计算列汇总
        const values = data.map((item) => {
          if (!item) return 0;

          if (item[column.property]) {
            return Number(item[column.property]) || 0;
          } else if (
            item["element"] &&
            typeof item["element"] === "object" &&
            item["element"][column.property]
          ) {
            return (
              proxy.$refs.formulaAddRef.computeElementContent(item["element"][column.property], item["amount"] || 0)
            );
          } else {
            return 0;
          }
        });

        if (!values.every((value) => Number.isNaN(value))) {
          const total = values
            .reduce((prev, curr) => {
              const value = Number(curr);
              return !Number.isNaN(value) ? prev + value : prev;
            }, 0)
            .toFixed(decimalPlaces);
          sums[index] = unpad(total);

          // 保存关键计算结果到临时变量
          if (column.property === "amount") {
            // 根据列标识符识别添加量列
            calculatedTotalAmount = Number(total);
          } else if (column.property === "cost") {
            // 根据列标识符识别成本列
            calculatedRawCost = Number(total);
          } else if (index >= 3 && column.property !== "price" && column.property !== "amount" && column.property !== "cost") {
            // 处理元素含量列 - 不是price、amount和cost的其他列
            const elementIndex = displayedElements.value.indexOf(column.property);
            if (elementIndex >= 0) {
              const element = column.property;
              const elementValue = Number(total);
              // 累计N+P+K值
              if (element === "N" || element === "P" || element === "K") {
                npkTotal += elementValue;
              }

              // 只有当有效的总量时才计算比例
              if (calculatedTotalAmount > 0) {
                const density =
                  !form.value.density || form.value.density == 0
                    ? 1
                    : form.value.density;
                const value =
                  (elementValue * defaultRatio.value) /
                  calculatedTotalAmount /
                  density;
                calculatedElements[element] = unpad(value.toFixed(decimalPlaces));
              }
            }
          }
        } else {
          sums[index] = "";
        }
      });

      // 添加N+P+K总量
      if (npkTotal > 0) {
        calculatedElements["N+P+K"] = unpad(npkTotal.toFixed(decimalPlaces));
      }

      // 使用防抖函数延迟更新响应式数据，避免递归触发
      // 使用独立于计算过程的函数进行更新
      updateFormStats(
        calculatedTotalAmount,
        calculatedRawCost,
        calculatedElements
      );

      return sums;
    } catch (err) {
      return [];
    }
  };

  function handleClick(tab) {
    activeTab.value = tab.props.name;
  }

  // 远程搜索配方的函数
  const remoteFormula = (query) => {
    if (query !== undefined) {
      loadingFormula.value = true;
      
      // 构建查询参数
      const params = {
        pageNo: 1,
        pageSize: 100,
        name: query || undefined, // 如果query为空字符串，设为undefined以便后端处理
        status: 3, // 状态3代表审核通过的配方
        quoteFlag: 0, // 仅查询不是报价的配方
      };
      
      
      // 设置短延时以防止过多请求
      setTimeout(() => {
        // 发起请求
        FormulaApi.getFormulaPage(params)
          .then((response) => {
            options.value = response.list || [];
            loadingFormula.value = false;
          })
          .catch(error => {
            options.value = [];
            loadingFormula.value = false;
            message.error("获取配方列表失败");
          });
      }, 200);
    } else {
      // 如果query为undefined，获取全部满足条件的配方（前100条）
      loadingFormula.value = true;
      
      const params = {
        pageNo: 1,
        pageSize: 100,
        status: 3, // 状态3代表审核通过的配方
      };
      
      FormulaApi.getFormulaPage(params)
        .then((response) => {
          options.value = response.list || [];
          loadingFormula.value = false;
        })
        .catch(error => {
          options.value = [];
          loadingFormula.value = false;
        });
    }
  };

  // 选择配方后的处理函数
  const changeFormula = (val) => {
    if (!val) {
      form.value = {}
      formulaId.value = null
      const formulaAddRef = proxy.$refs.formulaAddRef
      if(formulaAddRef && typeof formulaAddRef.clear === 'function'){
        formulaAddRef.clear()
      }
      return;
    }
    loadingFormula.value = true;
    FormulaApi.getFormula(val,true)
      .then((response) => {
        // 健壮性检查：response 本身有效
        if (response && response.id) {
          
          // 直接从 response 创建副本
          const formulaData = { ...response }; 
          
          // 将复制的数据赋值给 form.value
          Object.keys(form.value).forEach(key => { delete form.value[key]; });
          Object.assign(form.value, formulaData);
          
          isUpdate.value = true;
          formulaId.value = formulaData.id;
        } else {
          // 如果获取失败，清空选择并重置表单
          selectedFormula.value = null;
          reset();
        }
      })
      .catch((error) => {
        message.error("加载配方详情时出错: " + (error.message || "未知错误"));
        selectedFormula.value = null;
        reset();
      })
      .finally(() => {
        loadingFormula.value = false;
      });
  };

  // 格式化Excel数字
  function formatExcelNumber(value) {
    if (value === null || value === undefined || isNaN(Number(value))) {
      return "0.0000";
    }
    return Number(value).toFixed(4);
  }

  // 导出当前编辑中的配方为Excel
  const exportCurrentFormula = () => {
    // 调整检查条件：只要 form.value 存在，就认为基础数据有效
    if (!form.value) { 
      message.error("配方数据不存在，无法导出");
      return Promise.reject("配方数据不存在");
    }

    exporting.value = true;
    message.notify("正在准备导出数据...");

    try {
      // 获取当前配方数据
      const formulaData = {
        ...form.value,
        customerName: form.value.companyName || ""  // 确保公司名称正确传递到Excel表格第一行第二列
      };
      
      // 创建工作簿
      const workbook = new ExcelJS.Workbook();
      
      // 创建工作表
      const sheetName = formulaData.name || "配方导出";
      const sanitizedSheetName = sheetName.replace(/[*?:\\/\[\]]/g, '_');
      const worksheet = workbook.addWorksheet(sanitizedSheetName);
      
      // 设置列宽
      worksheet.columns = [
        { width: 30, wrapText: true }, // 原料名称列宽，允许换行
        { width: 25, wrapText: true }, // 单价列宽，允许换行
        { width: 25, wrapText: true }, // 添加量列宽，允许换行
        { width: 25, wrapText: true }, // 添加成本列宽，允许换行
      ];
      
      // 设置行高
      worksheet.properties.defaultRowHeight = 25;
      
      // 简化产品规格显示名称
      let productType = "";
      if (formulaData.stateCode) {
        productType = getDictLabel("product_state", formulaData.stateCode);
      }
      
      // 添加表头
      const headerRow = worksheet.addRow([
        productType || "", // 左上角填充产品类型信息
        formulaData.customerName || "", // 公司名
        formulaData.name || "", // 配方名称
        ""
      ]);
      
      // 设置表头样式
      headerRow.height = 40;
      headerRow.eachCell((cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
        cell.alignment = { 
          vertical: "middle", 
          horizontal: "center",
          wrapText: true // 允许文本自动换行
        };
        cell.font = { size: 12 };
      });
      
      // 第一个单元格 - 产品类型
      const stateCell = headerRow.getCell(1);
      stateCell.font = { size: 12, bold: true };
      
      // 第二个单元格 - 公司名称(红色)
      const companyCell = headerRow.getCell(2);
      companyCell.font = { 
        color: { argb: "FFFF0000" }, 
        size: 12,
        bold: true
      };
      
      // 合并第三、四列单元格作为配方名称
      worksheet.mergeCells("C1:D1");
      const formulaNameCell = headerRow.getCell(3);
      formulaNameCell.alignment = { 
        vertical: "middle", 
        horizontal: "center",
        wrapText: true // 允许文本自动换行
      };
      
      // 添加表格标题行
      const tableHeader = worksheet.addRow(["原料", "单价", "添加量", "添加成本"]);
      
      // 设置标题行样式
      tableHeader.height = 40;
      tableHeader.eachCell((cell) => {
        cell.font = { bold: true, size: 12 };
        cell.alignment = { 
          vertical: "middle", 
          horizontal: "center",
          wrapText: true
        };
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFF2F2F2" }, // 浅灰色背景
        };
      });
      
      // 添加配方材料数据
      const materials = formulaData.materials || [];
      let totalAmount = 0;
      let totalRawCost = 0;
      
      materials.forEach((material) => {
        // 跳过包装材料，不计入原料成本
        if (material.materialType === "4") {
          return;
        }
        
        const materialName = material.materialName || "";
        const price = material.price || 0;
        const amount = material.amount || 0;
        const cost = material.cost || 0;
        
        const dataRow = worksheet.addRow([
          materialName,
          `${formatExcelNumber(price)} 元`,
          `${Number(amount)}份`,
          `${formatExcelNumber(cost)} 元`,
        ]);
        
        // 设置数据行样式
        dataRow.height = 25;
        dataRow.eachCell((cell) => {
          cell.font = { size: 11 };
          cell.alignment = { 
            vertical: "middle", 
            horizontal: "center",
            wrapText: true
          };
          cell.border = {
            top: { style: "thin" },
            left: { style: "thin" },
            bottom: { style: "thin" },
            right: { style: "thin" },
          };
        });
        
        totalRawCost += Number(cost);
        totalAmount += Number(amount);
      });
      
      // 添加原料合计行
      const totalRow = worksheet.addRow([
        "合计",
        "",
        totalAmount + '份',
        `${formatExcelNumber(totalRawCost)} 元`, // 成本汇总现在只显示原料成本
      ]);
      
      // 设置合计行样式
      totalRow.height = 30;
      totalRow.eachCell((cell) => {
        cell.font = { bold: true, size: 12 };
        cell.alignment = { 
          vertical: "middle", 
          horizontal: "center",
          wrapText: true
        };
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFF2F2F2" }, // 浅灰色背景
        };
      });
      
      // 导出为Excel文件
      workbook.xlsx.writeBuffer()
        .then(buffer => {
          const blob = new Blob([buffer], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = `配方导出_${formulaData.name || '未命名'}_${new Date().getTime()}.xlsx`;
          a.click();
          window.URL.revokeObjectURL(url);
          exporting.value = false;
          message.success("导出成功");
        })
        .catch(err => {
          exporting.value = false;
          message.error("导出失败，请稍后重试");
        });
    } catch (error) {
      exporting.value = false;
      message.error("导出过程中发生错误");
    }
  };

  // 批量导出选中的配方记录
  const handleExport = () => {
    if (!ids.value || ids.value.length === 0) {
      message.error("请选择要导出的配方记录");
      return;
    }

    exporting.value = true;

    try {
      // 获取选中的所有配方数据
      const fetchPromises = ids.value.map(id => FormulaApi.getFormula(id,true));
      
      Promise.all(fetchPromises)
        .then(responses => {
          // 直接使用返回的 response 对象，并过滤掉无效的响应
          const formulaList = responses.filter(response => response && typeof response === 'object' && response.id); // 确保响应是包含 id 的有效对象
          
          if (formulaList.length === 0) {
            // 判断是全部失败还是部分失败
            if (responses.length > 0 && responses.every(r => !r)) {
               message.error("所有选中的配方数据都获取失败");
            } else {
               message.warning("部分配方数据获取失败或无效，已跳过");
               if (formulaList.length === 0) {
                   exporting.value = false;
                   return;
               }
            }
          }
          
          // 创建工作簿
          const workbook = new ExcelJS.Workbook();
          const usedSheetNames = new Set();
          
          // 为每个配方创建一个工作表
          formulaList.forEach((formulaData, index) => {
            if (!formulaData) return;
            
            // 确保公司名称正确传递
            formulaData.customerName = formulaData.companyName || "";
            
            // 生成工作表名称
            let baseSheetName = formulaData.name || `配方_${index + 1}`;
            baseSheetName = baseSheetName.length >= 26 ? baseSheetName.substring(0, 26) : baseSheetName;
            
            let sheetName = baseSheetName;
            let counter = 1;
            while (usedSheetNames.has(sheetName)) {
              sheetName = `${baseSheetName}_${counter}`;
              counter++;
            }
            usedSheetNames.add(sheetName);
            
            // 创建工作表
            const sanitizedSheetName = sheetName.replace(/[*?:\\/\[\]]/g, '_');

            const worksheet = workbook.addWorksheet(sanitizedSheetName);

            
            // 设置列宽
            worksheet.columns = [
              { width: 30, wrapText: true },
              { width: 25, wrapText: true },
              { width: 25, wrapText: true },
              { width: 25, wrapText: true },
            ];
            
            // 设置行高
            worksheet.properties.defaultRowHeight = 25;
            
            // 简化产品状态名称
            let productType = "";
            if (formulaData.stateCode) {
              productType = getDictLabel('product_state',formulaData.stateCode);
            }
            
            // 添加表头
            const headerRow = worksheet.addRow([
              productType || "", // 左上角填充产品类型
              formulaData.customerName || "",
              formulaData.name || "",
              ""
            ]);
            
            // 设置表头样式
            headerRow.height = 40;
            headerRow.eachCell((cell) => {
              cell.border = {
                top: { style: "thin" },
                left: { style: "thin" },
                bottom: { style: "thin" },
                right: { style: "thin" },
              };
              cell.alignment = { 
                vertical: "middle", 
                horizontal: "center",
                wrapText: true
              };
              cell.font = { size: 12 };
            });
            
            // 特别处理第二列（公司名称单元格）的字体颜色
            const companyCell = headerRow.getCell(2);
            companyCell.font = { 
              color: { argb: "FFFF0000" },
              size: 12,
              bold: true
            };
            
            // 合并第三、四列单元格作为配方名称
            worksheet.mergeCells("C1:D1");
            
            // 添加表格标题行
            const tableHeader = worksheet.addRow(["原料", "单价", "添加量", "添加成本"]);
            
            // 设置标题行样式
            tableHeader.height = 30;
            tableHeader.eachCell((cell) => {
              cell.font = { bold: true, size: 12 };
              cell.alignment = { 
                vertical: "middle", 
                horizontal: "center",
                wrapText: true
              };
              cell.border = {
                top: { style: "thin" },
                left: { style: "thin" },
                bottom: { style: "thin" },
                right: { style: "thin" },
              };
              cell.fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: "FFF2F2F2" },
              };
            });
            
            // 添加配方材料数据
            const materials = formulaData.materials || [];
            let totalAmount = 0;
            let totalRawCost = 0;
            
            materials.forEach((material) => {
              // 跳过包装材料，不计入原料成本
              if (material.materialType === "4") {
                return;
              }
              
              const materialName = material.materialName || "";
              const price = material.price || 0;
              const amount = material.amount || 0;
              const cost = material.cost || 0;
              
              const dataRow = worksheet.addRow([
                materialName,
                `${formatExcelNumber(price)} 元`,
                `${Number(amount)}份`,
                `${formatExcelNumber(cost)} 元`,
              ]);
              
              // 设置数据行样式
              dataRow.height = 25;
              dataRow.eachCell((cell) => {
                cell.font = { size: 11 };
                cell.alignment = { 
                  vertical: "middle", 
                  horizontal: "center",
                  wrapText: true
                };
                cell.border = {
                  top: { style: "thin" },
                  left: { style: "thin" },
                  bottom: { style: "thin" },
                  right: { style: "thin" },
                };
              });
              
              totalRawCost += Number(cost);
              totalAmount += Number(amount);
            });
            
            // 添加原料合计行
            const totalRow = worksheet.addRow([
              "合计",
              "",
              totalAmount + '份',
              `${formatExcelNumber(totalRawCost)} 元`, // 成本汇总现在只显示原料成本
            ]);
            
            // 设置合计行样式
            totalRow.height = 30;
            totalRow.eachCell((cell) => {
              cell.font = { bold: true, size: 12 };
              cell.alignment = { 
                vertical: "middle", 
                horizontal: "center",
                wrapText: true
              };
              cell.border = {
                top: { style: "thin" },
                left: { style: "thin" },
                bottom: { style: "thin" },
                right: { style: "thin" },
              };
              cell.fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: "FFF2F2F2" }, // 浅灰色背景
              };
            });
          });
          
          // 导出为Excel文件
          return workbook.xlsx.writeBuffer()
            .then(buffer => {
              const blob = new Blob([buffer], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              });
              const url = window.URL.createObjectURL(blob);
              const a = document.createElement("a");
              a.href = url;
              a.download = `配方批量导出_${new Date().getTime()}.xlsx`;
              a.click();
              window.URL.revokeObjectURL(url);
              exporting.value = false;
              message.success("导出成功");
            });
        })
        .catch(error => {
          exporting.value = false;
          console.log('error',error)
          message.error("批量导出过程中发生错误，请检查网络或联系管理员");
        });
    } catch (error) {
      exporting.value = false;
      message.error("导出失败，请稍后重试");
    }
  };



  // 预览并保存功能
  function handlePreviewAndSave() {
    console.log("handlePreviewAndSave 被调用");
    console.log("当前 form.value:", form.value);

    try {
      // 直接使用form.value作为表单数据
      const formData = form.value;

      if (!formData) {
        message.error("获取表单数据失败");
        return;
      }

      console.log("准备设置预览数据:", formData);

      // 设置预览数据
      previewData.value = { ...formData };
      previewOpen.value = true;

      console.log("预览弹窗应该已打开, previewOpen.value:", previewOpen.value);

    } catch (error) {
      console.error("获取表单数据时出错:", error);
      message.error("获取表单数据时出错: " + error.message);
    }
  }

  // 确认保存功能
  function confirmSave() {
    saving.value = true;

    // 调用原有的submitForm逻辑
    try {
      const formulaAddRef = proxy.$refs.formulaAddRef;
      let formData = null;

      if (typeof formulaAddRef.getFormData === 'function') {
        formData = formulaAddRef.getFormData();
      } else if (formulaAddRef.form) {
        formData = formulaAddRef.form;
      } else {
        formData = form.value;
      }

      if (!formData) {
        message.error("获取表单数据失败");
        saving.value = false;
        return;
      }

      formData.id = formulaId.value;
      if (formData.materials && Array.isArray(formData.materials)) {
        formData.materials = formData.materials.filter(item => item && item.materialName);
      }
      if (formData.packages && Array.isArray(formData.packages)) {
        formData.packages = formData.packages.filter(item => item && item.materialName);
      }

      // 处理更新操作
      if (formData.id != null) {
        // 确保所有关联数据的formulaId与配方id一致
        if (formData.materials && Array.isArray(formData.materials)) {
          formData.materials = formData.materials.map(material => {
            let processedElement = material.element;
            if (typeof material.element === 'string') {
              try {
                processedElement = JSON.parse(material.element);
              } catch (e) {
                processedElement = {};
              }
            } else if (!material.element || typeof material.element !== 'object') {
              processedElement = {};
            }

            return {
              ...material,
              element: processedElement,
              formulaId: formData.id
            };
          });
        }

        if (formData.packages && Array.isArray(formData.packages)) {
          formData.packages = formData.packages.map(pkg => {
            let processedElement = pkg.element;
            if (typeof pkg.element === 'string') {
              try {
                processedElement = JSON.parse(pkg.element);
              } catch (e) {
                processedElement = {};
              }
            } else if (!pkg.element || typeof pkg.element !== 'object') {
              processedElement = {};
            }

            const newPkg = {
              ...pkg,
              element: processedElement,
              formulaId: formData.id
            };

            if (pkg.materialId && !pkg.id) {
              newPkg.id = pkg.materialId;
            }

            return newPkg;
          });
        }

        if (formData.auxiliary && Array.isArray(formData.auxiliary)) {
          formData.auxiliary = formData.auxiliary.map(aux => ({
            ...aux,
            formulaId: formData.id
          }));
        }

        // 更新操作
        FormulaApi.updateFormula(formData).then(response => {
          message.success("修改成功");
          previewOpen.value = false;
          open.value = false;
          getList();
        }).catch(error => {
          message.error("更新失败");
        }).finally(() => {
          saving.value = false;
        });
      } else {
        // 新增操作
        FormulaApi.createFormula(formData).then(response => {
          message.success("新增成功");
          previewOpen.value = false;
          open.value = false;
          getList();
        }).catch(error => {
          message.error("新增失败");
        }).finally(() => {
          saving.value = false;
        });
      }
    } catch (error) {
      message.error("提交失败");
      saving.value = false;
    }
  }

  // 查看按钮操作
  function handleView(row) {
    reset();
    isCreateFormula.value = false;
    const _formulaId = row.id;
    if (!_formulaId) {
      message.error("请选择需要查看的记录");
      return;
    }
    formulaId.value = _formulaId;

    loading.value = true;

    // 传递 refresh=true, arg1=false
    FormulaApi.getFormula(_formulaId,true)
      .then((response) => {
        // 健壮性检查：response 本身有效
        if (response && response.id) {
          // 直接将整个 response 对象赋值
          Object.keys(form.value).forEach(key => { delete form.value[key]; });
          Object.assign(form.value, response);

          isUpdate.value = false; // 设置为查看模式
          title.value = "查看配方信息";
          open.value = true; // 打开弹窗

          // 确保 DOM 更新后激活正确的选项卡
          nextTick(() => {
            setTimeout(() => {
              activeTab.value = "basic";
              handleClick({ props: { name: "basic" } }); // 手动触发选项卡点击处理
            }, 100);
          });
        } else {
          message.error("获取配方数据失败：服务器未返回有效数据");
        }
      })
      .catch(error => {
        message.error("加载配方数据时出错: " + (error.message || "未知错误"));
      })
      .finally(() => {
        loading.value = false;
      });
  }

  // 加载初始数据
  onMounted(() => {
    getList();
    
  });

  // 返回所有需要在组件中使用的响应式数据和方法
  return {
    options,
    operate,
    product_category,
    product_state,
    approve_status,
    refresh,
    formulaList,
    formulaId,
    open,
    loading,
    showSearch,
    ids,
    single,
    multiple,
    total,
    title,
    defaultRatio,
    approveOpen,
    approveTitle,
    nowPage,
    exporting,
    isCreateFormula,
    selectedFormula,
    historyOpen,
    isUpdate,
    queryParams,
    form,
    approveRules,
    safeElements,
    elementTableData,
    elementColumns,
    filteredMicroElements,
    filteredOtherMaterials,
    previewFilteredMicroElements,
    previewFilteredOtherMaterials,
    previewOpen,
    previewData,
    saving,
    tableRowClassName,
    displayedElements,
    tableData,
    loadingFormula,
    getList,
    cancel,
    reset,
    handleQuery,
    resetQuery,
    handleSelectionChange,
    handleAdd,
    handleUpdate,
    submitForm,
    handleDelete,
    handleExport,
    handleApprove,
    approveForm,
    submitApprove,
    cancelApprove,
    openHistory,
    closeHistory,
    handleDialogClose,
    handleSubmitSuccess,
    handleSubmitClose,
    summaryMethod,
    handleClick,
    remoteFormula,
    changeFormula,
    exportCurrentFormula,
    handleDialogApprove,
    handleView,
    handlePreviewAndSave,
    confirmSave,
    handleSendToApprove,
    getDictLabel
  };
} 
