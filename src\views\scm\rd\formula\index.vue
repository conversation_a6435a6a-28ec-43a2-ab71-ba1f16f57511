<template>
  <ContentWrap>
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item label="配方名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入配方名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="企业名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品形态" prop="stateCode">
        <el-select
          v-model="queryParams.stateCode"
          style="width: 150px"
          placeholder="请选择产品形态"
          clearable
        >
          <el-option
            v-for="dict in product_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="通用名称" prop="typeCode">
        <el-select
          v-model="queryParams.typeCode"
          style="width: 150px"
          placeholder="请选择通用名称"
          clearable
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.PRODUCT_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="投入总份数" prop="totalAmount">
        <el-input
          v-model="queryParams.totalAmount"
          placeholder="请输入投入总份数"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          style="width: 150px"
          placeholder="请选择配方状态"
          clearable
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.APPROVE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="Number(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"><Icon icon="ep:search" />搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
        <el-button
          type="primary"
          plain
          @click="handleAdd"
          v-hasPermi="['quote:formula:create']"
        ><Icon icon="ep:plus" />新增</el-button>
        <el-button
          type="success"
          plain
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['quote:formula:update']"
        ><Icon icon="ep:edit" />修改</el-button>
         <el-button
          type="danger"
          plain
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['quote:formula:delete']"
        ><Icon icon="ep:delete" />删除</el-button>
        <el-button
          type="warning"
          plain
          @click="handleExport"
          v-hasPermi="['quote:formula:export']"
        ><Icon icon="ep:download" />导出</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="formulaList"
      @selection-change="handleSelectionChange" border
    >
      <el-table-column type="selection" width="40" align="center" />
      <!-- <el-table-column type="index" label="序号" width="50" align="center" /> -->
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column
        label="配方名称"
        align="left"
        prop="name"
        width="300"
        show-overflow-tooltip
      />
      <el-table-column label="版本" align="left" prop="version" width="60" />
      <el-table-column label="产品形态" align="left" prop="stateCode" width="90">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PRODUCT_STATE" :value="scope.row.stateCode" />
        </template>
      </el-table-column>
      <el-table-column
        label="通用名称"
        align="left"
        prop="typeCode"
        width="130"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PRODUCT_CATEGORY" :value="scope.row.typeCode" />
        </template>
      </el-table-column>
      <el-table-column label="投入份数" align="right" prop="totalAmount" width="90" />
      <el-table-column
        label="总成本"
        align="right"
        prop="totalCost"
        width="100"
      />
      <el-table-column
        label="工艺说明"
        align="left"
        prop="processDesc"
        show-overflow-tooltip
        width="180"
      />
      <el-table-column
        label="备注"
        align="left"
        prop="remark"
        show-overflow-tooltip
      />
      <el-table-column label="配方状态" align="left" prop="status" width="90">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.APPROVE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="提交人" align="left" prop="approveNo" width="100"/>
      <el-table-column label="提交时间" align="center" prop="createTime" width="160" :formatter="dateFormatter"/>
      <el-table-column label="审批人" align="left" prop="approverName" width="100"/>
      <el-table-column label="审批时间" align="left" prop="approveTime" width="160" :formatter="dateFormatter"/>
      <el-table-column
        label="操作"
        align="left"
        class-name="small-padding fixed-width"
        width="180"
        fixed="right"
        fit
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleView(scope.row)"
            v-hasPermi="['quote:formula:view']"
          ><Icon icon="ep:view" /></el-button>
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['quote:formula:update']"
          ><Icon icon="ep:edit" /></el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['quote:formula:delete']"
            v-if="scope.row.status < 3"
          ><Icon icon="ep:delete" /></el-button>
          <el-button
            link
            type="warning"
            @click="handleSendToApprove(scope.row)"
            v-hasPermi="['quote:formula:submit']"
            v-if="scope.row.status == 0"
          ><Icon icon="ep:promotion" />提交</el-button>
          <el-button
            link
            type="success"
            @click="handleApprove(scope.row)"
            v-hasPermi="['quote:formula:approve']"
            v-if="scope.row.status == 1"
          ><Icon icon="ep:select" />审批</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改配方信息对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="80%"
      append-to-body
      close-on-click-modal
      fullscreen
      :destroy-on-close="false"
      class="formula-dialog"
      @close="handleDialogClose"
    >
      <el-divider />
      <el-row
        :gutter="6"
        class="mb8"
        type="flex"
        align="middle"
        v-if="isCreateFormula"
      >
        <el-col :span="24">
          <div style="display: flex; align-items: center; white-space: nowrap">
            <el-text class="mx-1" type="success">从已有配方创建</el-text>
            <el-select
              v-model="selectedFormula"
              filterable
              remote
              reserve-keyword
              placeholder="请输入配方名称"
              :remote-method="remoteFormula"
              :loading="loadingFormula"
              style="width: 500px; margin-left: 20px"
              @change="changeFormula"
              clearable
            >
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
        </el-col>
      </el-row>

      <component
        :is="addComponent"
        ref="formulaAddRef"
        :formulaId="formulaId"
        :operate="operate"
        :applyId="form.applyId"
        :refresh="refresh"
        v-show="isUpdate"
        @update-success="handleSubmitSuccess"
        @submit-success="handleSubmitSuccess"
        @close-dialog="handleSubmitClose"
        :nowPage="nowPage"
      />

      <el-tabs v-model="activeTab" @tab-click="handleClick" v-show="!isUpdate">
        <el-tab-pane label="配方详情" name="detail">
          <div v-show="!isUpdate && !loading" class="tab-pane-content">
            <!-- 基本信息区域 -->
            <el-card class="box-card basic-info-card">
              <template #header>
                <div class="card-header">
                  <i class="el-icon-info"></i>
                  <span>配方基本信息</span>
                </div>
              </template>

              <el-descriptions
                :column="2"
                border
                class="info-descriptions compact-descriptions"
                size="small"
                :labelStyle="{
                  'font-weight': '600',
                  'min-width': '100px',
                  width: '120px',
                  'white-space': 'nowrap',
                }"
              >
                <el-descriptions-item label="配方名称" label-width="120px">
                  <span class="important-value">{{ form.name || "未设置" }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="产品形态">
                  <dict-tag
                    v-if="form.stateCode"
                    :type="DICT_TYPE.PRODUCT_STATE"
                    :value="form.stateCode"
                    size="small"
                  />
                  <span v-else class="empty-value">未设置</span>
                </el-descriptions-item>
                <el-descriptions-item label="通用名">
                  <dict-tag
                    v-if="form.typeCode"
                    :type="DICT_TYPE.PRODUCT_CATEGORY"
                    :value="form.typeCode"
                    size="small"
                  />
                  <span v-else class="empty-value">未设置</span>
                </el-descriptions-item>
                <el-descriptions-item label="企业">
                  <span>{{ form.companyName || "未设置" }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="氮磷钾含量" :span="2">
                  <div class="npk-container compact-npk">
                    <el-tag
                      class="npk-tag compact-tag"
                      type="success"
                      size="small"
                      v-if="form.npk && form.npk.N"
                    >
                      N: {{ form.npk.N }}{{ form.npk.n_unit || '%' }}
                    </el-tag>
                    <el-tag
                      class="npk-tag compact-tag"
                      type="warning"
                      size="small"
                      v-if="form.npk && form.npk.P"
                    >
                      {{ form.npk.PType || "P" }}: {{ form.npk.P }}{{ form.npk.p_unit || '%' }}
                    </el-tag>
                    <el-tag
                      class="npk-tag compact-tag"
                      type="danger"
                      size="small"
                      v-if="form.npk && form.npk.K"
                    >
                      K: {{ form.npk.K }}{{ form.npk.k_unit || '%'}}
                    </el-tag>
                    <span
                      v-if="
                        !form.npk || (!form.npk.N && !form.npk.P && !form.npk.K)
                      "
                      class="empty-value"
                      >未设置</span
                    >
                  </div>
                </el-descriptions-item>

                <!-- 中微量元素含量 - 紧凑显示 -->
                <el-descriptions-item label="中微量元素" :span="2">
                  <div class="compact-elements-container" v-if="filteredMicroElements.length > 0">
                    <el-tag
                      v-for="(item, index) in filteredMicroElements"
                      :key="index"
                      type="info"
                      effect="light"
                      size="small"
                      class="compact-element-tag"
                    >
                      {{ getDictLabel('medium_trace_element',item.element) }}: {{ item.quantity }}{{ item.unit }}
                    </el-tag>
                  </div>
                  <span v-else class="empty-value">无</span>
                </el-descriptions-item>

                <!-- 其他原料 - 紧凑显示 -->
                <el-descriptions-item label="其他原料" :span="2">
                  <div class="compact-elements-container" v-if="filteredOtherMaterials.length > 0">
                    <el-tag
                      v-for="(item, index) in filteredOtherMaterials"
                      :key="index"
                      type="success"
                      effect="light"
                      size="small"
                      class="compact-element-tag"
                    >
                      {{ getDictLabel('product_name_abbr',item.element) }}: {{ item.quantity }}{{ item.unit }}
                    </el-tag>
                  </div>
                  <span v-else class="empty-value">无</span>
                </el-descriptions-item>

                <el-descriptions-item label="工艺说明" :span="2">
                  <div class="formatted-text compact-text">{{ form.processDesc || "无" }}</div>
                </el-descriptions-item>
                <el-descriptions-item label="备注" :span="2">
                  <div class="formatted-text compact-text">{{ form.remark || "无" }}</div>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>

            <!-- 配方明细区域 -->
            <el-card class="box-card detail-card">
              <template #header>
                <div class="card-header">
                  <i class="el-icon-document"></i>
                  <span>配方材料清单</span>
                </div>
              </template>

              <!-- 自适应布局容器 -->
              <div class="adaptive-layout-container">
                <!-- 表格区域 -->
                <div class="table-section" :class="{ 'compact-table': isTableCompact }">

                  <!-- 表格水平滚动容器 -->
                  <div class="table-container">
                    <el-table
                      :data="tableData"
                      border
                      show-summary
                      :summary-method="summaryMethod"
                      style="width: 100%"
                      class="detail-table"
                      :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: 'bold',
                      }"
                      :row-class-name="tableRowClassName"
                      empty-text="暂无配方明细数据"
                    >
                      <el-table-column
                        type="index"
                        label="序号"
                        width="60"
                        fixed="left"
                      />
                      <el-table-column
                        prop="materialName"
                        label="原料名称"
                        width="180"
                        fixed="left"
                      >
                        <template #default="{ row }">
                          <span>{{ row.materialName || "未命名原料" }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="投入份数" width="100" prop="amount">
                        <template #default="{ row }">
                          <span>{{ row.amount || "0" }} 份</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        v-for="ele in displayedElements"
                        :key="ele"
                        :prop="ele"
                        :label="ele"
                        width="100"
                      >
                        <template #default="{ row }">
                          <span>{{ (row.element && row.element[ele]) || "0" }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="单价" width="140" prop="price" show-overflow-tooltip>
                        <template #default="{ row }">
                          <span>{{ row.price || "0" }} {{ row.priceUnit || "" }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="投入成本" width="140" prop="cost" fixed="right" show-overflow-tooltip>
                        <template #default="{ row }">
                          <span>{{ row.cost || "0" }} {{ row.priceUnit }}</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>

                <!-- 汇总信息区域 -->
                <div class="summary-section" :class="{ 'sidebar-summary': isTableCompact }">
                  <div class="section-header">
                    <i class="el-icon-data-analysis"></i>
                    <span>配方汇总信息</span>
                  </div>
                  <!-- 汇总信息卡片布局 -->
                  <div class="stats-wrapper" :class="{ 'sidebar-wrapper': isTableCompact }">
                    <div class="stats-container" :class="{ 'vertical-stats': isTableCompact }">
                    <div class="stat-card">
                      <el-statistic
                        title="投入总份数"
                        :value="Number(form.totalAmount || 0)"
                        :precision="4"
                      >
                        <template #suffix>
                          <span class="stat-unit">份</span>
                        </template>
                      </el-statistic>
                    </div>
                    <div class="stat-card">
                      <el-statistic
                        title="密度"
                        :value="Number(form.density || 1)"
                        :precision="4"
                      />
                    </div>
                    <div class="stat-card">
                      <el-statistic
                        title="PH值"
                        :value="Number(form.ph || 0)"
                        :precision="4"
                      />
                    </div>
                    <div class="stat-card highlight">
                      <el-statistic
                        title="总成本"
                        :value="Number(form.totalCost || 0)"
                        :precision="4"
                        suffix="元"
                      />
                    </div>
                    <div class="stat-card">
                      <el-statistic
                        title="总原料成本"
                        :value="Number(form.totalRawCost || 0)"
                        :precision="4"
                        suffix="元"
                      />
                    </div>
                  </div>
                  </div>
                </div>
              </div>

              <!-- 元素含量区域 - 只在有元素数据且值不全为0时显示 -->
              <div
                v-if="Object.keys(safeElements).length > 0 &&
                  Object.entries(safeElements).some(([key, value]) => parseFloat(value) > 0)"
              >
                <div class="section-header">
                  <i class="el-icon-data-analysis"></i>
                  <span>元素含量</span>
                </div>

                <el-table
                  :data="elementTableData"
                  border
                  stripe
                  class="elements-table"
                  :cell-style="{ textAlign: 'center' }"
                  :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#303133', fontWeight: '500', textAlign: 'center' }"
                >
                  <el-table-column
                    v-for="column in elementColumns"
                    :key="column.prop"
                    :prop="column.prop"
                    :label="column.label"
                    :width="column.width"
                  >
                    <template #default="scope">
                      <template v-if="column.type === 'progress'">
                        <div class="element-progress-cell">
                          <span class="element-value">{{ scope.row[column.prop] }}%</span>
                        </div>
                      </template>
                      <span v-else>{{ scope.row[column.prop] }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <!-- 预览模式下的按钮 -->
          <template v-if="form._isPreviewMode">
            <el-button
              type="primary"
              @click="handleBackToEdit"

            >
              <Icon icon="ep:edit" /> 返回编辑
            </el-button>
            <el-button
              type="primary"
              @click="exportCurrentFormula"
              class="export-button"
              :loading="exporting"
            >
              <i class="el-icon-download"></i> 导出Excel
            </el-button>
            <el-button @click="cancel" class="cancel-button">
              关 闭
            </el-button>
          </template>

          <!-- 编辑模式下的按钮 -->
          <template v-else>
            <el-button
              v-if="isUpdate"
              type="info"
              @click="handlePreviewCurrentFormula"
              class="preview-button"
              :disabled="!form.id"
            >
              <Icon icon="ep:view" /> 预览
            </el-button>
            <el-button
              type="primary"
              @click="exportCurrentFormula"
              class="export-button"
              :loading="exporting"
            >
              <i class="el-icon-download"></i> 导出Excel
            </el-button>
            <el-button
              v-if="isUpdate"
              type="primary"
              @click="submitForm"
              v-hasPermi="['quote:formula:update']"
              class="submit-button"
            >
              <i class="el-icon-check"></i> 保存
            </el-button>
            <!-- <el-button
              type="primary"
              @click="handleDialogApprove"
              v-hasPermi="['rd:formula:approve']"
              v-if="form.value.status === 1" >
              <i class="el-icon-document-checked"></i> 审核
            </el-button> -->
            <el-button @click="cancel" class="cancel-button">
              {{ !isUpdate ? "关 闭" : "取 消" }}
            </el-button>
          </template>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      :title="approveTitle"
      v-model="approveOpen"
      append-to-body
      :destroy-on-close="false"
      :close-on-click-modal="false"
      width="500px"
    >
      <el-form
        ref="approveRef"
        :model="approveForm"
        :rules="approveRules"
        label-width="auto"
      >
        <el-form-item label="配方名称" prop="name">
          <el-input v-model="approveForm.name" placeholder="请输入内容" disabled />
        </el-form-item>
        <el-form-item label="审批状态" prop="status">
          <el-select v-model="approveForm.status" placeholder="选择审批状态">
            <el-option
              v-for="dict in approve_status"
              :key="dict.value"
              :label="dict.label"
              :value="Number(dict.value)"
              :disabled="Number(dict.value) !== 3 && Number(dict.value) !== 4"
            >
              <span style="float: left">{{ dict.label }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审批意见" prop="approveDesc">
          <el-input
            v-model="approveForm.approveDesc"
            type="textarea"
            placeholder="请输入内容"
            rows="4"
            autosize
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="submitApprove"
            v-hasPermi="['quote:formula:approve']"

            >确 定</el-button
          >
          <el-button @click="cancelApprove">{{
            !isUpdate ? "关闭" : "取消"
          }}</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      title="查看历史配方"
      v-model="historyOpen"
      append-to-body
      close-on-click-modal
      fullscreen
      width="800px"
    >
      <component :is="historyComponent" :formulaName="form.companyName" />
    </el-dialog>
  </ContentWrap>
</template>

<script setup name="Formula">
import { useFormulaFunctions } from './index.js';
import formulaHistory from "@/views/scm/rd/formula/history.vue";
import add from "./add.vue";
import { ref, getCurrentInstance, computed } from 'vue';
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict.ts';
import Icon from '@/components/Icon/src/Icon.vue';
import { getDictLabel } from '@/utils/dict.ts';
const instance = getCurrentInstance();
const proxy = instance?.proxy;

import { dateFormatter } from '@/utils/formatTime'
// 引入组件
const addComponent = ref(add);
const historyComponent = ref(formulaHistory);
const activeTab = ref("detail");
// 使用封装在index.js中的函数
const {
  options,
  operate,
  product_category,
  product_state,
  approve_status,
  refresh,
  formulaList,
  formulaId,
  open,
  loading,
  showSearch,
  ids,
  single,
  multiple,
  total,
  title,
  defaultRatio,
  approveOpen,
  approveStatus,
  approveTitle,
  nowPage,
  exporting,
  isCreateFormula,
  selectedFormula,
  historyOpen,
  isUpdate,
  queryParams,
  form,
  approveRules,
  approveForm,
  safeElements,
  elementTableData,
  elementColumns,
  filteredMicroElements,
  filteredOtherMaterials,
  tableRowClassName,
  displayedElements,
  tableData,
  loadingFormula,
  getList,
  cancel,
  reset,
  handleQuery,
  resetQuery,
  handleSelectionChange,
  handleAdd,
  handleUpdate,
  submitForm,
  handleDelete,
  handleExport,
  handleApprove,
  handleSendToApprove,
  submitApprove,
  cancelApprove,
  openHistory,
  closeHistory,
  handleDialogClose,
  handleSubmitSuccess,
  handleSubmitClose,
  summaryMethod,
  exportCurrentFormula,
  handleDialogApprove,
  remoteFormula,
  changeFormula,
  handleView,
  handlePreviewCurrentFormula,
  handleBackToEdit
} = useFormulaFunctions(proxy);

// 计算表格是否应该紧凑显示
const isTableCompact = computed(() => {
  // 如果没有表格数据，返回false
  if (!tableData.value || tableData.value.length === 0) {
    return false;
  }

  // 计算表格所需的最小宽度
  const baseWidth = 60 + 180 + 100 + 140 + 140; // 序号 + 原料名称 + 投入份数 + 单价 + 投入成本
  const elementWidth = (displayedElements.value?.length || 0) * 100; // 元素列宽度
  const totalTableWidth = baseWidth + elementWidth;

  // 如果表格宽度加上汇总区域宽度小于容器宽度，则可以紧凑显示
  // 汇总区域宽度约320px（280px + 20px gap + 20px margin）
  const summaryWidth = 320;
  const containerWidth = 1200; // 假设容器宽度

  return (totalTableWidth + summaryWidth) < containerWidth;
});

</script>

<style scoped>

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-15px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shine {
  to {
    left: 100%;
  }
}

/* 响应式适配 */
@media (width <= 1200px) {
  .elements-container {
    gap: 8px; /* 减小间距 */
  }

  .element-card {
    flex: 1 1 160px; /* 减小尺寸 */
    min-width: 140px; /* 减小尺寸 */
  }

  :deep(.stat-card .el-statistic__content) {
    font-size: 20px; /* 减小字体 */
  }

  .element-value {
    font-size: 16px; /* 减小字体 */
  }
}

@media (width <= 768px) {
  .elements-container {
    gap: 8px; /* 减小间距 */
  }

  .element-card {
    min-width: 110px; /* 减小尺寸 */
    padding: 10px; /* 减小内边距 */
    flex: 1 1 120px; /* 减小尺寸 */
  }

  .stat-card {
    padding: 10px; /* 减小内边距 */
  }

  :deep(.stat-card .el-statistic__content) {
    font-size: 18px; /* 减小字体 */
  }

  .element-value {
    font-size: 14px; /* 减小字体 */
  }

  :deep(.info-descriptions .el-descriptions__label),
  :deep(.info-descriptions .el-descriptions__content) {
    padding: 8px 10px; /* 减小内边距 */
  }

  .dialog-footer .el-button {
    min-width: 90px; /* 减小宽度 */
    padding: 8px 16px; /* 减小内边距 */
  }

  :deep(.info-descriptions .el-descriptions__label) {
    width: 90px; /* 减小宽度 */
    min-width: 90px; /* 减小宽度 */
  }
}

@media (width <= 576px) {
  .element-card {
    flex: 1 1 100%;
    min-width: 100%;
  }

  .stats-container {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  :deep(.info-descriptions) {
    margin-bottom: 10px; /* 减小间距 */
  }

  :deep(.detail-card .el-card__body) {
    padding: 12px; /* 减小内边距 */
  }

  .dialog-footer {
    flex-direction: column;
  }

  .dialog-footer .el-button {
    width: 100%;
  }
}

/* 打印样式优化 */
@media print {
  .detail-view {
    width: 100%;
    padding: 0;
    margin: 0;
  }

  .detail-card,
  .stat-card,
  .element-card,
  :deep(.info-descriptions .el-descriptions__body) {
    border: 1px solid #ebeef5;
    box-shadow: none;
    page-break-inside: avoid;
  }

  .dialog-footer {
    display: none;
  }

  .element-tag {
    border: 1px solid #ebeef5;
    box-shadow: none;
  }
}

/* 确保导出按钮在移动设备上也有良好展示 */
@media (width <= 576px) {
  .export-button {
    width: 100%;
    margin-bottom: 8px; /* 减小间距 */
  }
}

:deep(.formula-dialog) {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  --card-shadow: 0 3px 12px rgb(0 0 0 / 10%);
  --transition-time: 0.3s;
}

/* 整体弹窗优化 */
:deep(.formula-dialog .el-dialog__header) {
  position: relative;
  padding: 12px 20px;  /* 减小内边距 */
  margin-right: 0;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
}

:deep(.formula-dialog .el-dialog__header::after) {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--primary-color);
  content: "";
}

:deep(.formula-dialog .el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

:deep(.formula-dialog .el-dialog__body) {
  padding: 15px 20px; /* 减小内边距 */
  color: #333;
  background-color: #fff;
}

:deep(.formula-dialog .el-dialog__footer) {
  padding: 12px 20px; /* 减小内边距 */
  background-color: #fff;
  border-top: 1px solid #ebeef5;
}

/* 添加Tab内容容器样式 */
.tab-pane-content {
  padding: 0; /* 移除内边距 */
  animation: fadeIn 0.3s ease-in-out;
}

/* 确保选项卡内容占满空间 */
:deep(.el-tabs__content) {
  min-height: 200px; /* 减小最小高度 */
}

/* 基本信息部分美化 */
.info-descriptions {
  animation: fadeIn 0.6s ease-in-out;
}

:deep(.info-descriptions .el-descriptions__header) {
  padding: 8px; /* 减小内边距 */
  margin-bottom: 12px; /* 减小间距 */
  background: #fff;
  border-left: 3px solid var(--primary-color);
  border-radius: 4px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 8%);
}

:deep(.info-descriptions .el-descriptions__title) {
  font-size: 15px; /* 减小字体 */
  font-weight: 600;
  color: #303133;
}

:deep(.info-descriptions .el-descriptions__body) {
  overflow: hidden;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 8%);
}

:deep(.info-descriptions .el-descriptions__label) {
  width: 80px;
  min-width: 80px;
  padding: 5px 8px !important;
  font-size: 13px !important;
  font-weight: 500;
  color: #303133;
  background-color: #f5f7fa;
}

:deep(.info-descriptions .el-descriptions__content) {
  padding: 5px 8px !important;
  font-size: 13px !important;
  line-height: 1.4; /* 减小行高 */
  color: #606266;
}

/* 紧凑描述样式 */
.compact-descriptions {
  margin-bottom: 12px;
}

:deep(.compact-descriptions .el-descriptions__label) {
  width: 100px !important;
  min-width: 100px !important;
  padding: 6px 8px !important;
  font-size: 12px !important;
  font-weight: 500;
  color: #303133;
  background-color: #f8f9fa;
}

:deep(.compact-descriptions .el-descriptions__content) {
  padding: 6px 8px !important;
  font-size: 12px !important;
  line-height: 1.3;
  color: #606266;
}

/* 紧凑元素容器 */
.compact-elements-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

/* 紧凑元素标签 */
.compact-element-tag {
  padding: 2px 6px !important;
  margin: 0 !important;
  font-size: 11px !important;
  line-height: 1.2 !important;
  border-radius: 2px !important;
}

/* 紧凑NPK容器 */
.compact-npk {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

/* 紧凑标签 */
.compact-tag {
  padding: 2px 6px !important;
  margin: 0 !important;
  font-size: 11px !important;
  line-height: 1.2 !important;
  border-radius: 2px !important;
}

/* 紧凑文本 */
.compact-text {
  max-height: 60px;
  padding: 4px 6px;
  font-size: 12px;
  line-height: 1.3;
  overflow-y: auto;
  background-color: #fafafa;
  border-radius: 3px;
  border: 1px solid #e9ecef;
}

/* 元素标签美化 */
.element-tag {
  padding: 2px 4px;
  margin: 2px;
  font-size: 13px;
}

.element-name {
  margin-right: 3px;
  font-size: 13px;
}

.element-value {
  font-size: 13px;
}

/* 移除背景色，统一样式 */
.micro-element-tag {
  color: #303133;
  border-left: 3px solid #409EFF;
  border-color: #dcdfe6;
}

.other-material-tag {
  color: #303133;
  border-color: #dcdfe6;
}

/* 详情页面整体样式 */
.detail-view {
  animation: fadeIn 0.4s ease-in-out;
}

/* 基本信息卡片样式 */
.basic-info-card {
  margin-bottom: 16px; /* 与配方明细卡片的间距 */
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 8%);
}

:deep(.basic-info-card .el-card__body) {
  padding: 16px; /* 基本信息区域稍大的内边距 */
}

:deep(.basic-info-card .el-card__header) {
  padding: 12px 16px; /* 统一header内边距 */
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

/* 配方明细卡片样式 */
.detail-card {
  margin-bottom: 0; /* 移除底部间距 */
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 8%);
}

:deep(.detail-card .el-card__body) {
  padding: 12px; /* 适中的内边距 */
}

:deep(.detail-card .el-card__header) {
  padding: 12px 16px; /* 统一header内边距 */
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.card-header {
  display: flex;
  margin-bottom: 10px; /* 减小间距 */
  font-size: 15px; /* 减小字体 */
  font-weight: 500;
  color: #303133;
  align-items: center;
}

.card-header i {
  margin-right: 6px; /* 减小间距 */
  font-size: 15px; /* 减小字体 */
  color: var(--primary-color);
}

/* 统一字体大小和样式 */
.formatted-text {
  max-height: 80px;
  min-height: auto;
  font-size: 13px;
  line-height: 1.4; /* 减小行高 */
  color: #606266;
  white-space: pre-wrap;
}

/* 数据统计卡片统一样式 */
.stat-card {
  min-height: 80px;
  padding: 12px;
  margin-bottom: 0;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgb(0 0 0 / 12%);
  transform: translateY(-2px);
}

.stat-card.highlight {
  border-left: 4px solid #409eff;
}

/* 垂直排列模式下的统计卡片样式 */
.vertical-stats .stat-card {
  min-height: 70px; /* 适中的高度 */
  padding: 10px 12px; /* 适中的内边距 */
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.vertical-stats .stat-card:deep(.el-statistic__title) {
  font-size: 12px !important; /* 减小标题字体 */
  margin-bottom: 4px !important;
  line-height: 1.2;
  color: #666;
}

.vertical-stats .stat-card:deep(.el-statistic__content) {
  font-size: 16px !important; /* 适中的数值字体 */
  line-height: 1.2;
  font-weight: 600;
}

.vertical-stats .stat-card .stat-unit {
  font-size: 12px; /* 适中的单位字体 */
  color: #999;
}

:deep(.stat-card .el-statistic__title) {
  margin-bottom: 3px;
  font-size: 13px !important;
  font-weight: 500;
  color: #606266;
}

:deep(.stat-card .el-statistic__content) {
  font-size: 15px !important;
}

.stat-unit {
  margin-left: 3px; /* 减小间距 */
  font-size: 12px; /* 减小字体 */
  color: #909399;
}

/* 分割线样式统一 */
:deep(.el-divider--horizontal) {
  margin: 8px 0;
}

:deep(.el-divider__text) {
  padding: 0 8px;
  font-size: 14px;
}

/* 元素含量卡片统一样式 */
.elements-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* 减小间距 */
  margin-bottom: 12px; /* 减小间距 */
}

.element-card {
  min-width: 90px; /* 减小尺寸 */
  padding: 8px; /* 减小内边距 */
  background-color: #fff;
  border-left: 3px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 8%);
  flex: 1 1 90px; /* 减小尺寸 */
}

/* NPK元素特别样式 */
.n-element {
  border-left: 3px solid #67C23A;
}

.p-element {
  border-left: 3px solid #E6A23C;
}

.k-element {
  border-left: 3px solid #F56C6C;
}

.npk-element {
  border-left: 3px solid #409EFF;
}

.element-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px; /* 减小间距 */
  align-items: center;
}

.element-symbol {
  font-size: 14px; /* 减小字体 */
  font-weight: 500;
  color: #303133;
}

.element-value {
  font-size: 14px; /* 减小字体 */
  font-weight: 500;
  color: #303133;
}

/* 底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px; /* 减小间距 */
}

.dialog-footer .el-button {
  min-width: 100px; /* 减小宽度 */
  padding: 10px 20px; /* 减小内边距 */
  font-weight: 500;
  letter-spacing: 0.5px;
  border-radius: 20px; /* 减小圆角 */
  transition: all var(--transition-time);
}

.dialog-footer .el-button i {
  margin-right: 4px; /* 减小间距 */
}

.dialog-footer .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgb(0 0 0 / 15%); /* 减小阴影 */
}

.submit-button:hover {
  background-color: #85ce61;
  border-color: #85ce61;
}

.cancel-button {
  background-color: white;
  border-color: #dcdfe6;
}

.cancel-button:hover {
  color: var(--primary-color);
  background-color: #ecf5ff;
  border-color: #c6e2ff;
}

/* 增加查看弹窗的适应性样式 */

/* 空数据值样式 */
.empty-value {
  font-style: italic;
  color: #909399;
}

/* 重要信息突出显示 */
.important-value {
  font-size: 14px; /* 减小字体 */
  font-weight: 600;
  color: #303133;
}

/* NPK标签容器 */
.npk-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* 减小间距 */
  margin-bottom: 12px; /* 减小间距 */
}

.npk-tag {
  display: inline-flex;
  padding: 6px 10px; /* 减小内边距 */
  font-weight: 500;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 3px; /* 减小圆角 */
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
  align-items: center;
}

.npk-tag.n-tag {
  border-left: 3px solid #67C23A;
}

.npk-tag.p-tag {
  border-left: 3px solid #E6A23C;
}

.npk-tag.k-tag {
  border-left: 3px solid #F56C6C;
}

.element-name {
  margin-right: 5px; /* 减小间距 */
  font-size: 13px; /* 减小字体 */
  font-weight: 500;
  color: #303133;
}

.element-value {
  font-size: 13px; /* 减小字体 */
  font-weight: 500;
  color: #303133;
}

/* 无元素含量数据时的显示 */
.empty-elements-message {
  display: flex;
  width: 100%;
  padding: 16px;
  color: #909399;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 13px;
}

.empty-elements-message i {
  font-size: 16px;
  color: #c0c4cc;
}

/* 增强元素卡片的灵活显示 */
.element-card {
  display: flex;
  flex-direction: column;
  min-height: 80px; /* 减小高度 */
  justify-content: space-between;
}

/* 确保统计卡片高度一致 */
.stat-card {
  display: flex;
  min-height: 80px; /* 减小高度 */
  flex-direction: column;
  justify-content: center;
}

/* 优化长文本显示 */
.formatted-text {
  max-height: 120px; /* 减小高度 */
  padding: 8px;
  overflow-y: auto;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  line-height: 1.5;
  font-size: 13px;
  color: #495057;
}

/* 优化表格适应性 */
:deep(.detail-table) {
  width: 100%;
  height: auto !important;
}

:deep(.detail-table .el-table__body-wrapper) {
  max-height: calc(100vh - 320px);
  overflow-y: auto;
}

:deep(.detail-table th),
:deep(.detail-table td) {
  padding: 4px !important;
  font-size: 13px !important;
}

:deep(.detail-table .el-table__row) {
  height: 32px !important;
}

/* 导出按钮增强样式 */
.export-button {
  position: relative;
  display: flex;
  overflow: hidden;
  color: white;
  align-items: center;
  justify-content: center;
}

.export-button i {
  margin-right: 6px; /* 减小间距 */
  font-size: 14px; /* 减小字体 */
}

.export-button:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgb(0 0 0 / 20%); /* 减小阴影 */
}

.export-button:hover::after {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgb(255 255 255 / 0%) 0%,
    rgb(255 255 255 / 30%) 50%,
    rgb(255 255 255 / 0%) 100%
  );
  content: "";
  animation: shine 1.5s infinite;
}

/* 导出按钮加载状态样式 */
.export-button.is-loading {
  color: white;
  cursor: not-allowed;
  background-color: #b3d8ff;
  border-color: #b3d8ff;
}

.dialog-footer{
  margin-top: 15px; /* 减小间距 */
}

/* 添加元素表格样式 */
.elements-table {
  margin-bottom: 8px;
}

.element-progress-cell {
  display: flex;
  flex-direction: column;
  padding: 6px 0; /* 减小内边距 */
}

.element-progress-cell .element-value {
  margin-bottom: 6px; /* 减小间距 */
  font-size: 13px; /* 减小字体 */
  font-weight: 500;
  color: #303133;
}

:deep(.elements-table .el-table__row) {
  height: 30px !important;
}

:deep(.elements-table .cell) {
  padding: 4px;
  font-size: 13px !important;
}

/* 审核按钮样式 */
.approve-button {
  position: relative;
  display: flex;
  overflow: hidden;
  color: white;
  align-items: center;
  justify-content: center;
}

.approve-button i {
  margin-right: 6px; /* 减小间距 */
  font-size: 14px; /* 减小字体 */
}

.approve-button:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgb(0 0 0 / 20%); /* 减小阴影 */
}

/* 表格水平滚动容器 */
.table-container {
  width: 100%;
  overflow-x: auto;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.compact-table .table-container {
  width: fit-content; /* 由表格内容撑开 */
  min-width: 100%; /* 至少占满父容器 */
}

/* 确保固定列样式正确 */
:deep(.el-table .el-table__fixed) {
  height: auto !important;
  box-shadow: 0 0 10px rgb(0 0 0 / 10%);
}

:deep(.el-table .el-table__fixed-right) {
  height: auto !important;
  box-shadow: -3px 0 10px rgb(0 0 0 / 10%);
}

/* 优化表格在横向滚动时的样式 */
:deep(.detail-table) {
  width: auto;
  min-width: 100%;
}

/* 紧凑模式下的表格样式 */
.compact-table :deep(.detail-table) {
  width: auto; /* 由内容撑开 */
  min-width: auto; /* 移除最小宽度限制 */
}

/* 确保合计行样式正确 */
:deep(.el-table__footer-wrapper) {
  z-index: 1;
}

/* 合计行上边框 */
:deep(.el-table__footer-wrapper) tr td {
  font-weight: bold;
  background-color: #F8F8F9;
  border-top: 1px solid #EBEEF5 !important;
}

/* 自适应布局样式 */
.adaptive-layout-container {
  display: flex;
  gap: 20px;
  width: 100%;
  align-items: flex-start; /* 顶部对齐 */
  overflow-x: auto; /* 允许水平滚动 */
}

/* 表格区域 */
.table-section {
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
  display: flex;
  flex-direction: column;
}

.table-section.compact-table {
  flex: 0 0 auto; /* 不伸缩，由内容撑开 */
  min-width: 0; /* 允许收缩 */
  width: fit-content; /* 由内容撑开宽度 */
}

/* 汇总信息区域 */
.summary-section {
  flex: 0 0 100%; /* 默认占满整行 */
  margin-top: 20px;
}

.summary-section.sidebar-summary {
  flex: 1; /* 占用剩余空间 */
  margin-top: 0;
  margin-left: 20px;
  min-width: 240px; /* 设置最小宽度 */
  align-self: flex-start; /* 顶部对齐，不强制拉伸 */
  display: flex;
  flex-direction: column;
}

/* 统计容器包装器 */
.stats-wrapper {
  display: flex;
  flex-direction: column;
}

/* 统计容器 */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 10px;
  width: 100%;
  max-width: 1000px; /* 限制最大宽度，避免过度拉伸 */
}

.stats-container.vertical-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 10px;
  width: 100%;
  align-content: start; /* 内容从顶部开始排列 */
  grid-auto-rows: min-content; /* 行高根据内容调整 */
}

/* 统一的卡片标题样式 */
.card-header {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  height: 24px; /* 固定高度确保一致性 */
  line-height: 24px;
}

.card-header i {
  margin-right: 8px;
  font-size: 15px;
  color: #409eff;
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 6px;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  height: 24px; /* 固定高度确保一致性 */
  line-height: 24px;
}

.section-header i {
  margin-right: 8px;
  font-size: 16px;
  color: #409eff;
}

/* 侧边栏模式下的区域标题 */
.sidebar-summary .section-header {
  margin-bottom: 12px;
  font-size: 14px;
}

.sidebar-summary .section-header i {
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .adaptive-layout-container {
    flex-direction: column;
    gap: 12px;
  }

  .table-section.compact-table {
    max-width: 100%;
  }

  .summary-section.sidebar-summary {
    flex: 0 0 auto;
    margin-left: 0;
    margin-top: 12px;
    min-width: auto;
  }

  .stats-container.vertical-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
  }

  .vertical-stats .stat-card {
    min-height: 65px;
    padding: 8px 10px;
  }

  .vertical-stats .stat-card:deep(.el-statistic__content) {
    font-size: 14px !important;
  }
}

@media (max-width: 768px) {
  .adaptive-layout-container {
    gap: 10px;
  }

  .stats-container,
  .stats-container.vertical-stats {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .section-header {
    font-size: 14px;
    margin-bottom: 8px;
    padding-bottom: 4px;
  }

  /* 小屏幕下基本信息优化 */
  .basic-info-card {
    margin-bottom: 12px;
  }

  :deep(.basic-info-card .el-card__body) {
    padding: 12px;
  }

  :deep(.basic-info-card .el-card__header),
  :deep(.detail-card .el-card__header) {
    padding: 10px 12px; /* 小屏幕下减小header内边距 */
  }

  .card-header {
    font-size: 14px; /* 小屏幕下减小字体 */
    height: 20px; /* 小屏幕下减小高度 */
    line-height: 20px;
  }

  .card-header i {
    font-size: 14px;
  }

  .info-descriptions {
    font-size: 13px;
  }

  /* 小屏幕下滚动条优化 */
  .table-container::-webkit-scrollbar,
  .adaptive-layout-container::-webkit-scrollbar,
  .formatted-text::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  :deep(.el-dialog__body)::-webkit-scrollbar {
    width: 6px;
  }
}

/* 底部内容自适应布局样式 */
.bottom-content-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.bottom-content-container.side-by-side {
  flex-direction: row;
  gap: 20px;
  align-items: flex-start;
}

/* 元素含量区域 */
.elements-section {
  flex: 1;
  min-width: 0;
}

.elements-section.compact-elements {
  flex: 0 0 auto;
  width: fit-content;
  min-width: 300px;
  max-width: 50%;
}

/* 补充信息区域 */
.supplement-section {
  flex: 1;
  min-width: 0;
}

.supplement-section.compact-supplement {
  flex: 1;
  min-width: 300px;
}

/* 元素含量表格样式优化 */
.elements-table {
  width: 100%;
}

/* 元素含量表格滚动条 */
:deep(.elements-table .el-table__body-wrapper)::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

:deep(.elements-table .el-table__body-wrapper)::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

:deep(.elements-table .el-table__body-wrapper)::-webkit-scrollbar-thumb {
  background: #dee2e6;
  border-radius: 3px;
}

:deep(.elements-table .el-table__body-wrapper)::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
}

/* 统一滚动条样式 */
/* Webkit浏览器滚动条样式 */
.table-container::-webkit-scrollbar,
.adaptive-layout-container::-webkit-scrollbar,
.formatted-text::-webkit-scrollbar,
:deep(.detail-table .el-table__body-wrapper)::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-track,
.adaptive-layout-container::-webkit-scrollbar-track,
.formatted-text::-webkit-scrollbar-track,
:deep(.detail-table .el-table__body-wrapper)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb,
.adaptive-layout-container::-webkit-scrollbar-thumb,
.formatted-text::-webkit-scrollbar-thumb,
:deep(.detail-table .el-table__body-wrapper)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.table-container::-webkit-scrollbar-thumb:hover,
.adaptive-layout-container::-webkit-scrollbar-thumb:hover,
.formatted-text::-webkit-scrollbar-thumb:hover,
:deep(.detail-table .el-table__body-wrapper)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.table-container::-webkit-scrollbar-corner,
.adaptive-layout-container::-webkit-scrollbar-corner,
.formatted-text::-webkit-scrollbar-corner,
:deep(.detail-table .el-table__body-wrapper)::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

/* Firefox滚动条样式 */
.table-container,
.adaptive-layout-container,
.formatted-text,
:deep(.detail-table .el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 弹窗整体滚动条样式 */
:deep(.el-dialog__body)::-webkit-scrollbar {
  width: 8px;
}

:deep(.el-dialog__body)::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-dialog__body)::-webkit-scrollbar-thumb {
  background: #d0d0d0;
  border-radius: 4px;
  transition: background 0.3s ease;
}

:deep(.el-dialog__body)::-webkit-scrollbar-thumb:hover {
  background: #b0b0b0;
}

/* Tab内容滚动条样式 */
:deep(.el-tabs__content)::-webkit-scrollbar {
  width: 6px;
}

:deep(.el-tabs__content)::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

:deep(.el-tabs__content)::-webkit-scrollbar-thumb {
  background: #dee2e6;
  border-radius: 3px;
  transition: background 0.3s ease;
}

:deep(.el-tabs__content)::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
}

/* 表格内容滚动条特殊样式 */
:deep(.el-table .el-table__body-wrapper)::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

:deep(.el-table .el-table__body-wrapper)::-webkit-scrollbar-track {
  background: #fafafa;
  border-radius: 4px;
}

:deep(.el-table .el-table__body-wrapper)::-webkit-scrollbar-thumb {
  background: #e0e0e0;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

:deep(.el-table .el-table__body-wrapper)::-webkit-scrollbar-thumb:hover {
  background: #d0d0d0;
}

:deep(.el-table .el-table__body-wrapper)::-webkit-scrollbar-corner {
  background: #fafafa;
}
</style>
